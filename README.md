# 🧵 CottonXpert - Textile Sourcing & Inspection Hub

> **Premium textile sourcing and quality inspection services from Karachi, Pakistan to global markets.**

## 🚀 **Quick Start**

### **Windows:**
```bash
start-website.bat
```

### **Manual Start:**
```bash
npm install
npm run dev
```

**Website:** http://localhost:8080
**Product Catalog:** http://localhost:8080/catalog

## 📧 **Email-Powered Contact Form**

This application uses **EmailJS** for direct email notifications:

- **📧 Direct Email** - Messages <NAME_EMAIL>
- **🚀 Simple Setup** - No database or admin portal needed
- **⚡ Fast Response** - Immediate email notifications
- **🔒 Secure** - Client-side email service
- **💰 Free** - Up to 200 emails/month

## ✨ **Features**

### **🌐 Public Website**
- **Responsive Design** - Mobile-first approach
- **Product Showcase** - Interactive product cards with modals
- **Contact Form** - Direct email notifications
- **Mobile Navigation** - Working hamburger menu
- **Product Catalog** - Complete product listing with search

### **📧 Email Integration**
- **Direct Email Delivery** - Messages <NAME_EMAIL>
- **EmailJS Service** - Reliable email delivery
- **Custom Templates** - Professional email formatting
- **Reply Directly** - Respond from your email client
- **No Database Needed** - Simple and efficient

### **✨ Key Features**
- **Professional Design** - Modern, clean interface
- **Mobile Responsive** - Works on all devices
- **Fast Loading** - Optimized performance
- **Easy Setup** - No complex backend required
- **Reliable Email** - Direct inbox delivery

## 🛠️ **Setup Instructions**

### **Immediate Use:**
1. **Start website:** `start-website.bat` or `npm run dev`
2. **Test contact form** - it works immediately (shows success message)
3. **Configure email** (optional) - follow EMAIL_SETUP.md for email delivery

### **Email Setup (Optional):**
1. **Create EmailJS account** at [EmailJS.com](https://www.emailjs.com/)
2. **Connect Gmail service** to send emails
3. **Create email template** for contact form
4. **Update contact form** with your EmailJS credentials
5. **Test email delivery** to <EMAIL>

## 📁 **Key Files**

- `EMAIL_SETUP.md` - Complete EmailJS setup guide
- `start-website.bat` - Easy startup script
- `src/components/Contact.tsx` - Contact form component
- `.env.local` - Environment configuration

## ✅ **What Works Right Now**

- ✅ **Professional website** with responsive design
- ✅ **Product showcase** with interactive modals
- ✅ **Contact form** with validation and success messages
- ✅ **Mobile navigation** with hamburger menu
- ✅ **Product catalog** with search functionality

## 📧 **Email Setup Benefits**

Once you configure EmailJS (5 minutes):
- ✅ **Direct email delivery** to <EMAIL>
- ✅ **Professional email templates** with customer details
- ✅ **Reply directly** from your email client
- ✅ **No admin portal needed** - everything in your inbox
- ✅ **Free service** (up to 200 emails/month)

**Start with:** `start-website.bat` 🚀
