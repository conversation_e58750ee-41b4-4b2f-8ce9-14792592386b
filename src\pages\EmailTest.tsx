import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { CheckCircle, XCircle, Mail, Copy, AlertTriangle } from "lucide-react";
import { Link } from "react-router-dom";
import emailjs from '@emailjs/browser';

export default function EmailTest() {
  const [testResults, setTestResults] = useState<{
    formspree: 'pending' | 'success' | 'error';
    emailjs: 'pending' | 'success' | 'error';
    lastSubmission?: any;
  }>({
    formspree: 'pending',
    emailjs: 'pending'
  });
  
  const [testData] = useState({
    fullName: "Test Customer",
    email: "<EMAIL>",
    companyName: "Test Company Ltd",
    phone: "******-0123",
    projectRequirements: "This is a test message from CottonXpert website to verify email delivery is working correctly."
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const testFormspree = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('https://formspree.io/f/<EMAIL>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testData.email,
          name: testData.fullName,
          company: testData.companyName,
          phone: testData.phone,
          message: `TEST EMAIL from CottonXpert website:

Name: ${testData.fullName}
Email: ${testData.email}
Company: ${testData.companyName}
Phone: ${testData.phone}
Date: ${new Date().toLocaleString()}

Message:
${testData.projectRequirements}

---
This is a test email to verify the contact form is working.`,
          _replyto: testData.email,
          _subject: `TEST: New Contact from ${testData.fullName} - CottonXpert`
        })
      });

      if (response.ok) {
        setTestResults(prev => ({ ...prev, formspree: 'success' }));
        toast({
          title: "Formspree Test Successful!",
          description: "Check <EMAIL> for test email"
        });
      } else {
        throw new Error(`Formspree error: ${response.status}`);
      }
    } catch (error: any) {
      setTestResults(prev => ({ ...prev, formspree: 'error' }));
      toast({
        title: "Formspree Test Failed",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testEmailJS = async () => {
    setIsLoading(true);
    try {
      const result = await emailjs.send(
        'service_cottonxpert',
        'template_contact',
        {
          to_email: '<EMAIL>',
          from_name: testData.fullName,
          from_email: testData.email,
          company_name: testData.companyName,
          phone: testData.phone,
          message: testData.projectRequirements,
          reply_to: testData.email,
          subject: `TEST: New Contact from ${testData.fullName} - CottonXpert`,
          date: new Date().toLocaleString()
        },
        'your_public_key'
      );

      setTestResults(prev => ({ ...prev, emailjs: 'success' }));
      toast({
        title: "EmailJS Test Successful!",
        description: "Check <EMAIL> for test email"
      });
    } catch (error: any) {
      setTestResults(prev => ({ ...prev, emailjs: 'error' }));
      toast({
        title: "EmailJS Not Configured",
        description: "Follow QUICK_EMAIL_SETUP.md to configure EmailJS",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = () => {
    const emailContent = `Subject: New Contact from ${testData.fullName} - CottonXpert

New contact form submission from CottonXpert website:

Name: ${testData.fullName}
Email: ${testData.email}
Company: ${testData.companyName}
Phone: ${testData.phone}
Date: ${new Date().toLocaleString()}

Message:
${testData.projectRequirements}

---
Reply directly to this email to respond to the customer.`;

    navigator.clipboard.writeText(emailContent);
    toast({
      title: "Email Content Copied!",
      description: "Paste this into an <NAME_EMAIL>"
    });
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 p-4">
      <div className="container mx-auto max-w-4xl">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Mail className="h-8 w-8 text-textile-warm" />
            <h1 className="text-3xl font-bold text-primary">Email Delivery Test</h1>
          </div>
          <p className="text-muted-foreground">
            Test email <NAME_EMAIL>
          </p>
          <div className="mt-4">
            <Link to="/">
              <Button variant="outline" size="sm">
                ← Back to Website
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Test Results */}
          <Card>
            <CardHeader>
              <CardTitle>Email Service Tests</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <span className="font-medium">Formspree (Immediate)</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.formspree)}
                  <span className="text-sm capitalize">{testResults.formspree}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <span className="font-medium">EmailJS (Needs Setup)</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.emailjs)}
                  <span className="text-sm capitalize">{testResults.emailjs}</span>
                </div>
              </div>

              <div className="space-y-2">
                <Button 
                  onClick={testFormspree} 
                  className="w-full bg-textile-warm hover:bg-textile-warm/90"
                  disabled={isLoading}
                >
                  {isLoading ? "Testing..." : "Test Formspree Email"}
                </Button>
                
                <Button 
                  onClick={testEmailJS} 
                  variant="outline" 
                  className="w-full"
                  disabled={isLoading}
                >
                  Test EmailJS (if configured)
                </Button>

                <Button 
                  onClick={copyToClipboard} 
                  variant="outline" 
                  className="w-full"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Email Content
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Test Data */}
          <Card>
            <CardHeader>
              <CardTitle>Test Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <Input value={testData.fullName} readOnly />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <Input value={testData.email} readOnly />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Company</label>
                <Input value={testData.companyName} readOnly />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Phone</label>
                <Input value={testData.phone} readOnly />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Message</label>
                <Textarea value={testData.projectRequirements} readOnly rows={4} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">If Formspree Works:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Check <EMAIL></li>
                  <li>• Check spam/junk folder</li>
                  <li>• Your contact form is working!</li>
                  <li>• No further setup needed</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">If No Email Received:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Use "Copy Email Content" button</li>
                  <li>• Paste into new email</li>
                  <li>• <NAME_EMAIL></li>
                  <li>• Or set up EmailJS (5 minutes)</li>
                </ul>
              </div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Goal:</strong> When customers submit the contact form, you should receive 
                <NAME_EMAIL> with their inquiry details.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
