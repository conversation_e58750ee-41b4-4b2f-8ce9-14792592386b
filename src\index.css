@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 215 28% 17%;
    --primary-foreground: 0 0% 98%;

    --secondary: 30 25% 85%;
    --secondary-foreground: 215 25% 27%;

    --muted: 210 20% 94%;
    --muted-foreground: 215 16% 47%;

    --accent: 24 70% 56%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 215 20% 88%;
    --input: 215 20% 88%;
    --ring: 215 28% 17%;

    /* Custom textile design tokens */
    --textile-navy: 215 28% 17%;
    --textile-warm: 24 70% 56%;
    --textile-earth: 30 25% 75%;
    --textile-cream: 45 25% 95%;
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--textile-navy)) 0%, hsl(var(--primary)) 100%);
    --gradient-section: linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--textile-cream)) 100%);
    --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    
    /* Shadows */
    --shadow-textile: 0 10px 40px -10px hsl(var(--textile-navy) / 0.1);
    --shadow-card: 0 4px 20px -4px hsl(var(--textile-navy) / 0.08);
    --shadow-hero: 0 25px 50px -12px hsl(var(--textile-navy) / 0.25);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 215 28% 12%;
    --foreground: 0 0% 95%;

    --card: 215 28% 15%;
    --card-foreground: 0 0% 95%;

    --popover: 215 28% 15%;
    --popover-foreground: 0 0% 95%;

    --primary: 0 0% 95%;
    --primary-foreground: 215 28% 17%;

    --secondary: 215 25% 20%;
    --secondary-foreground: 0 0% 95%;

    --muted: 215 25% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 24 70% 56%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 62% 50%;
    --destructive-foreground: 0 0% 95%;

    --border: 215 25% 20%;
    --input: 215 25% 20%;
    --ring: 215 28% 80%;

    /* Dark mode textile tokens */
    --textile-navy: 215 28% 80%;
    --textile-warm: 24 70% 60%;
    --textile-earth: 30 25% 30%;
    --textile-cream: 215 25% 18%;
    
    --gradient-hero: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--textile-earth)) 100%);
    --gradient-section: linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--textile-cream)) 100%);
    --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}