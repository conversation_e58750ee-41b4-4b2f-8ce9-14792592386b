-- Create a function to trigger email notifications
CREATE OR REPLACE FUNCTION public.handle_new_contact_message()
RETURNS TRIGGER AS $$
BEGIN
  -- Call the Edge Function to send email
  PERFORM
    net.http_post(
      url := 'https://jpcsnrhauubzfpxrzczt.supabase.co/functions/v1/send-contact-email',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key', true)
      ),
      body := jsonb_build_object('record', to_jsonb(NEW))
    );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate trigger to call the function after insert
CREATE TRIGGER on_contact_message_created
  AFTER INSERT ON public.contact_messages
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_contact_message();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA net TO postgres, anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION net.http_post TO postgres, anon, authenticated, service_role;
