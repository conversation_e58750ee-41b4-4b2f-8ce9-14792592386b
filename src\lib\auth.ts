import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  User,
  createUserWithEmailAndPassword
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from './firebase';

// Admin user interface
export interface AdminUser {
  uid: string;
  email: string;
  role: 'admin' | 'super_admin';
  created_at: Date;
  last_login?: Date;
}

// Authentication service
export const authService = {
  // Sign in admin user
  async signIn(email: string, password: string): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      // Input validation
      if (!email || !password) {
        return { success: false, error: 'Email and password are required' };
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Check if user is admin
      const isAdmin = await this.isUserAdmin(user.uid);
      if (!isAdmin) {
        await signOut(auth);
        return { success: false, error: 'Access denied. Admin privileges required.' };
      }

      // Update last login
      await this.updateLastLogin(user.uid);

      return { success: true, user };
    } catch (error: any) {
      console.error('Sign in error:', error);
      
      // Handle specific Firebase auth errors
      let errorMessage = 'Sign in failed';
      switch (error.code) {
        case 'auth/user-not-found':
        case 'auth/wrong-password':
          errorMessage = 'Invalid email or password';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later.';
          break;
        case 'auth/network-request-failed':
          errorMessage = 'Network error. Please check your connection.';
          break;
        default:
          errorMessage = error.message || 'Sign in failed';
      }
      
      return { success: false, error: errorMessage };
    }
  },

  // Sign out
  async signOut(): Promise<{ success: boolean; error?: string }> {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error: any) {
      console.error('Sign out error:', error);
      return { success: false, error: error.message || 'Sign out failed' };
    }
  },

  // Check if user is admin
  async isUserAdmin(uid: string): Promise<boolean> {
    try {
      const adminDoc = await getDoc(doc(db, 'admin_users', uid));
      return adminDoc.exists();
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  },

  // Create admin user (super admin only)
  async createAdminUser(email: string, password: string, role: 'admin' | 'super_admin' = 'admin'): Promise<{
    success: boolean;
    uid?: string;
    error?: string;
  }> {
    try {
      // Input validation
      if (!email || !password) {
        return { success: false, error: 'Email and password are required' };
      }

      if (password.length < 8) {
        return { success: false, error: 'Password must be at least 8 characters long' };
      }

      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Create admin user document
      const adminUser: AdminUser = {
        uid: user.uid,
        email: user.email!,
        role,
        created_at: new Date()
      };

      await setDoc(doc(db, 'admin_users', user.uid), adminUser);

      return { success: true, uid: user.uid };
    } catch (error: any) {
      console.error('Error creating admin user:', error);
      
      let errorMessage = 'Failed to create admin user';
      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = 'Email is already in use';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address';
          break;
        case 'auth/weak-password':
          errorMessage = 'Password is too weak';
          break;
        default:
          errorMessage = error.message || 'Failed to create admin user';
      }
      
      return { success: false, error: errorMessage };
    }
  },

  // Update last login timestamp
  async updateLastLogin(uid: string): Promise<void> {
    try {
      const adminRef = doc(db, 'admin_users', uid);
      await setDoc(adminRef, { last_login: new Date() }, { merge: true });
    } catch (error) {
      console.error('Error updating last login:', error);
    }
  },

  // Get current user
  getCurrentUser(): User | null {
    return auth.currentUser;
  },

  // Listen to auth state changes
  onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, callback);
  }
};

// Security utilities
export const securityUtils = {
  // Rate limiting for login attempts (client-side basic implementation)
  loginAttempts: new Map<string, { count: number; lastAttempt: number }>(),

  checkRateLimit(email: string): boolean {
    const now = Date.now();
    const attempts = this.loginAttempts.get(email);
    
    if (!attempts) {
      this.loginAttempts.set(email, { count: 1, lastAttempt: now });
      return true;
    }

    // Reset after 15 minutes
    if (now - attempts.lastAttempt > 15 * 60 * 1000) {
      this.loginAttempts.set(email, { count: 1, lastAttempt: now });
      return true;
    }

    // Allow max 5 attempts per 15 minutes
    if (attempts.count >= 5) {
      return false;
    }

    attempts.count++;
    attempts.lastAttempt = now;
    return true;
  },

  // Clear rate limit on successful login
  clearRateLimit(email: string): void {
    this.loginAttempts.delete(email);
  },

  // Validate environment variables
  validateEnvironment(): { isValid: boolean; missingVars: string[] } {
    const requiredVars = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_AUTH_DOMAIN',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_FIREBASE_STORAGE_BUCKET',
      'VITE_FIREBASE_MESSAGING_SENDER_ID',
      'VITE_FIREBASE_APP_ID'
    ];

    const missingVars = requiredVars.filter(varName => !import.meta.env[varName]);
    
    return {
      isValid: missingVars.length === 0,
      missingVars
    };
  }
};
