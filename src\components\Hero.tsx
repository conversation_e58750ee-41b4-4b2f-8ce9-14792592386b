import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>R<PERSON>, CheckCircle, Star } from "lucide-react";
import heroBg from "@/assets/hero-bg.jpg";

export function Hero() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center">
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${heroBg})` }}
      >
        <div className="absolute inset-0 bg-gradient-hero opacity-90"></div>
      </div>

      <div className="relative container mx-auto px-4 py-20">
        <div className="max-w-4xl mx-auto text-center text-white">
          <Badge className="mb-6 bg-textile-warm/20 text-textile-cream border-textile-warm/30">
            <Star className="h-4 w-4 mr-2" />
            20+ Years of Excellence
          </Badge>

          <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
            Premium Textile
            <span className="block text-textile-warm">Sourcing & Inspection</span>
          </h1>

          <p className="text-xl md:text-2xl mb-8 text-textile-cream max-w-3xl mx-auto animate-slide-up">
            Your trusted partner for institutional and retail linen with comprehensive
            quality assurance from Karachi, Pakistan to global markets.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12 animate-scale-in">
            <Button
              variant="hero"
              size="lg"
              className="group"
              onClick={() => scrollToSection('products')}
            >
              Explore Our Products
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="bg-white/10 border-white/30 text-white hover:bg-white/20"
              onClick={() => scrollToSection('contact')}
            >
              Contact Us Today
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-2">
              <CheckCircle className="h-5 w-5 text-textile-warm" />
              <span className="text-textile-cream">ISO Certified</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <CheckCircle className="h-5 w-5 text-textile-warm" />
              <span className="text-textile-cream">Global Partnerships</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <CheckCircle className="h-5 w-5 text-textile-warm" />
              <span className="text-textile-cream">Quality Assured</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}