# 🔥 Firebase Setup Guide for CottonXpert

## 🚀 Quick Setup (10 Minutes)

### Step 1: Create Firebase Project
1. **Go to [Firebase Console](https://console.firebase.google.com/)**
2. **Click "Create a project"**
3. **Project name:** `cottonxpert-textile-hub`
4. **Enable Google Analytics** (optional)
5. **Click "Create project"**

### Step 2: Enable Required Services

#### **Firestore Database:**
1. **Go to Firestore Database** in the left sidebar
2. **Click "Create database"**
3. **Choose "Start in production mode"** (we have security rules)
4. **Select your region** (closest to your users)

#### **Authentication:**
1. **Go to Authentication** in the left sidebar
2. **Click "Get started"**
3. **Go to "Sign-in method" tab**
4. **Enable "Email/Password"**

### Step 3: Get Configuration Keys
1. **Go to Project Settings** (gear icon)
2. **Scroll down to "Your apps"**
3. **Click "Web app" icon (</>)**
4. **App nickname:** `cottonxpert-web`
5. **Copy the configuration object**

### Step 4: Configure Environment Variables
1. **Copy `.env.example` to `.env.local`**
2. **Replace the values with your Firebase config:**

```env
# Replace these with your actual Firebase configuration
VITE_FIREBASE_API_KEY=AIzaSyC...
VITE_FIREBASE_AUTH_DOMAIN=cottonxpert-textile-hub.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=cottonxpert-textile-hub
VITE_FIREBASE_STORAGE_BUCKET=cottonxpert-textile-hub.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456
VITE_FIREBASE_MEASUREMENT_ID=G-ABCDEF1234

VITE_USE_FIREBASE_EMULATOR=false
VITE_ENVIRONMENT=production
VITE_EMAIL_TO=<EMAIL>
```

### Step 5: Deploy Security Rules
1. **Install Firebase CLI:**
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase:**
   ```bash
   firebase login
   ```

3. **Initialize Firebase in your project:**
   ```bash
   firebase init firestore
   ```
   - Select your project
   - Use `firestore.rules` for rules file
   - Use `firestore.indexes.json` for indexes

4. **Deploy security rules:**
   ```bash
   firebase deploy --only firestore:rules
   ```

### Step 6: Create Admin User
1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Go to `/admin` and try to login** (will fail first time)

3. **Create admin user via Firebase Console:**
   - Go to Authentication > Users
   - Click "Add user"
   - Email: `<EMAIL>`
   - Password: `SecurePassword123!`

4. **Add admin document to Firestore:**
   - Go to Firestore Database
   - Create collection: `admin_users`
   - Document ID: (copy the UID from Authentication)
   - Fields:
     ```
     uid: [user-uid-from-auth]
     email: <EMAIL>
     role: super_admin
     created_at: [current timestamp]
     ```

## 🔒 Security Features Implemented

### **Firestore Security Rules:**
- ✅ **Public contact form submissions** (anyone can create)
- ✅ **Admin-only message reading** (authentication required)
- ✅ **Input validation** (XSS protection, length limits)
- ✅ **Rate limiting** (basic spam protection)

### **Authentication Security:**
- ✅ **Email/password authentication**
- ✅ **Admin role verification**
- ✅ **Rate limiting** (5 attempts per 15 minutes)
- ✅ **Secure password requirements**
- ✅ **Session management**

### **Environment Security:**
- ✅ **Environment variables** for sensitive data
- ✅ **No hardcoded credentials**
- ✅ **Development/production separation**
- ✅ **Validation of required variables**

## 🛡️ Security Best Practices

### **Environment Variables:**
```bash
# Never commit these files:
.env.local
.env.production
.env.development

# Always use VITE_ prefix for client-side variables
VITE_FIREBASE_API_KEY=...
```

### **Firestore Rules:**
```javascript
// Example of our security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /contact_messages/{messageId} {
      allow create: if isValidContactMessage(resource.data);
      allow read, update, delete: if isAuthenticated() && isAdmin();
    }
  }
}
```

### **Authentication:**
```typescript
// Rate limiting implementation
checkRateLimit(email: string): boolean {
  const attempts = this.loginAttempts.get(email);
  if (attempts && attempts.count >= 5) {
    return false; // Block after 5 attempts
  }
  return true;
}
```

## 🚫 Closed Ports & Optimizations

### **Removed Services:**
- ❌ **Supabase** (completely removed)
- ❌ **Email server on port 3002** (replaced with Firebase Functions)
- ❌ **Unused dependencies** (cleaned up)

### **Optimized Configuration:**
- ✅ **Single Firebase backend** (no multiple services)
- ✅ **Client-side only** (no separate backend server)
- ✅ **Secure environment handling**
- ✅ **Production-ready deployment**

## 📧 Email Notifications Setup

### **Option 1: Firebase Functions (Recommended)**
1. **Enable Firebase Functions:**
   ```bash
   firebase init functions
   ```

2. **Install dependencies:**
   ```bash
   cd functions
   npm install nodemailer
   ```

3. **Create email function:**
   ```javascript
   exports.sendContactEmail = functions.firestore
     .document('contact_messages/{messageId}')
     .onCreate(async (snap, context) => {
       // Send email using nodemailer
     });
   ```

### **Option 2: Third-party Service**
- **EmailJS** (client-side)
- **Formspree** (form handling)
- **Netlify Forms** (if hosting on Netlify)

## 🧪 Testing Your Setup

### **1. Test Contact Form:**
```bash
# Start development server
npm run dev

# Go to http://localhost:8080
# Fill out contact form
# Check Firestore for new document
```

### **2. Test Admin Login:**
```bash
# Go to http://localhost:8080/admin
# Login with admin credentials
# Verify you can see messages
```

### **3. Test Security:**
```bash
# Try accessing admin without login
# Try submitting invalid form data
# Check rate limiting with multiple login attempts
```

## 🚀 Production Deployment

### **Environment Variables for Production:**
```env
VITE_FIREBASE_API_KEY=your-production-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_ENVIRONMENT=production
```

### **Build and Deploy:**
```bash
# Build for production
npm run build

# Deploy to Firebase Hosting (optional)
firebase init hosting
firebase deploy
```

## 🔧 Troubleshooting

### **Common Issues:**

1. **"Firebase not initialized"**
   - Check `.env.local` file exists
   - Verify all VITE_ variables are set
   - Restart development server

2. **"Permission denied" in Firestore**
   - Deploy security rules: `firebase deploy --only firestore:rules`
   - Check admin user exists in `admin_users` collection

3. **Login fails**
   - Verify admin user exists in Authentication
   - Check admin document in Firestore
   - Clear browser cache/localStorage

4. **Contact form not saving**
   - Check browser console for errors
   - Verify Firestore rules allow create
   - Check network tab for failed requests

## 📞 Support

If you encounter issues:
1. **Check browser console** for error messages
2. **Verify Firebase configuration** in console
3. **Test with Firebase emulator** for development
4. **Check Firestore rules** are deployed correctly

---

**🎉 Your CottonXpert website is now secured with Firebase and ready for production!**
