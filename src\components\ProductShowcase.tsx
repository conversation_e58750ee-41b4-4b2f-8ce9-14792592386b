import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Eye } from "lucide-react";
import { Link } from "react-router-dom";
import { ProductModal } from "./ProductModal";

import terryLinen from "@/assets/terry-linen.jpg";
import bedLinen from "@/assets/bed-linen.jpg";
import blankets from "@/assets/blankets.jpg";
import uniforms from "@/assets/uniforms.jpg";
import fabrics from "@/assets/fabrics.jpg";

const products = [
  {
    title: "Terry Linen",
    description: "Premium quality terry towels and bathrobes for hospitality and healthcare sectors.",
    image: terry<PERSON>inen,
    category: "Hotel & Healthcare",
    features: ["100% Cotton", "Absorbent", "Durable"]
  },
  {
    title: "Bed Linen",
    description: "Luxury bed sheets, pillowcases, and duvet covers for hotels and retail.",
    image: bedLinen,
    category: "Hospitality",
    features: ["Thread Count 200-800", "Wrinkle Free", "Color Fast"]
  },
  {
    title: "Blankets & Throws",
    description: "Cozy blankets and decorative throws for comfort and style.",
    image: blankets,
    category: "Comfort",
    features: ["Multiple Weights", "Hypoallergenic", "Machine Washable"]
  },
  {
    title: "Uniforms",
    description: "Professional uniforms for healthcare, hospitality, and corporate sectors.",
    image: uniforms,
    category: "Professional",
    features: ["Custom Design", "Durable Fabric", "Easy Care"]
  },
  {
    title: "Fabrics & Materials",
    description: "Raw materials and fabric supplies for textile manufacturing.",
    image: fabrics,
    category: "Manufacturing",
    features: ["Bulk Orders", "Quality Tested", "Global Sourcing"]
  }
];

export function ProductShowcase() {
  const [selectedProduct, setSelectedProduct] = useState<typeof products[0] | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleLearnMore = (product: typeof products[0]) => {
    setSelectedProduct(product);
    setIsModalOpen(true);
  };



  return (
    <section id="products" className="py-20 bg-gradient-section">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-textile-warm/10 text-textile-warm border-textile-warm/20">
            Our Product Range
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-primary">
            Premium Textile Solutions
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            From institutional linen to retail textiles, we source and inspect
            the finest quality products to meet your specific requirements.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {products.map((product, index) => (
            <Card 
              key={product.title} 
              className="group hover:shadow-card-hover transition-all duration-300 hover:-translate-y-2 bg-gradient-card"
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img 
                    src={product.image} 
                    alt={product.title}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <Button 
                    size="icon"
                    variant="ghost"
                    className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <Badge variant="secondary" className="text-xs">
                      {product.category}
                    </Badge>
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-2 text-primary">
                    {product.title}
                  </h3>
                  
                  <p className="text-muted-foreground mb-4 text-sm">
                    {product.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {product.features.map((feature, idx) => (
                      <span 
                        key={idx}
                        className="text-xs bg-textile-earth/20 text-textile-navy px-2 py-1 rounded-full"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full group/btn"
                    onClick={() => handleLearnMore(product)}
                  >
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Link to="/catalog">
            <Button
              variant="premium"
              size="lg"
              className="group"
            >
              View Complete Catalog
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>

      <ProductModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        product={selectedProduct}
      />
    </section>
  );
}