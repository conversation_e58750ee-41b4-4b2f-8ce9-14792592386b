const express = require('express');
const nodemailer = require('nodemailer');
const cors = require('cors');
const bodyParser = require('body-parser');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:8080',
  credentials: true
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Create nodemailer transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS, // Use App Password for Gmail
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

// Email template function
const createEmailTemplate = (contactData) => {
  const { fullName, email, companyName, phone, projectRequirements } = contactData;
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Contact Form Submission - CottonXpert</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9; }
        .email-content { background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 3px solid #d4a574; padding-bottom: 20px; }
        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }
        .header p { color: #7f8c8d; margin: 5px 0 0 0; }
        .contact-details { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .contact-details h2 { color: #2c3e50; margin-top: 0; }
        .detail-row { display: flex; margin-bottom: 10px; }
        .detail-label { font-weight: bold; color: #34495e; width: 120px; }
        .detail-value { color: #2c3e50; }
        .requirements { background-color: #e8f4f8; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }
        .requirements h3 { color: #2c3e50; margin-top: 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ecf0f1; }
        .footer p { color: #7f8c8d; font-size: 14px; margin: 0; }
        .btn { display: inline-block; padding: 12px 24px; background-color: #d4a574; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background-color: #c19660; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="email-content">
          <div class="header">
            <h1>🧵 CottonXpert</h1>
            <p>New Contact Form Submission</p>
          </div>
          
          <div class="contact-details">
            <h2>Contact Information</h2>
            <div class="detail-row">
              <span class="detail-label">Name:</span>
              <span class="detail-value">${fullName}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Email:</span>
              <span class="detail-value"><a href="mailto:${email}" style="color: #3498db;">${email}</a></span>
            </div>
            ${companyName ? `
            <div class="detail-row">
              <span class="detail-label">Company:</span>
              <span class="detail-value">${companyName}</span>
            </div>
            ` : ''}
            ${phone ? `
            <div class="detail-row">
              <span class="detail-label">Phone:</span>
              <span class="detail-value"><a href="tel:${phone}" style="color: #3498db;">${phone}</a></span>
            </div>
            ` : ''}
            <div class="detail-row">
              <span class="detail-label">Date:</span>
              <span class="detail-value">${new Date().toLocaleString()}</span>
            </div>
          </div>
          
          <div class="requirements">
            <h3>Project Requirements</h3>
            <p>${projectRequirements}</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="mailto:${email}" class="btn">Reply to Customer</a>
            <a href="${process.env.FRONTEND_URL}/admin" class="btn">View Admin Dashboard</a>
          </div>
          
          <div class="footer">
            <p>This email was sent automatically from your CottonXpert website contact form.</p>
            <p style="margin-top: 10px; font-size: 12px;">
              Visit your <a href="${process.env.FRONTEND_URL}/admin" style="color: #3498db;">admin dashboard</a> to manage all messages.
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

// Test email endpoint
app.get('/api/test-email', async (req, res) => {
  try {
    const transporter = createTransporter();
    
    // Verify connection
    await transporter.verify();
    
    res.json({ 
      success: true, 
      message: 'Email server is configured correctly!' 
    });
  } catch (error) {
    console.error('Email configuration error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Email server configuration failed',
      details: error.message 
    });
  }
});

// Send email endpoint
app.post('/api/send-email', async (req, res) => {
  try {
    const { fullName, email, companyName, phone, projectRequirements } = req.body;
    
    // Validate required fields
    if (!fullName || !email || !projectRequirements) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: fullName, email, and projectRequirements are required'
      });
    }
    
    const transporter = createTransporter();
    
    // Email options
    const mailOptions = {
      from: `"CottonXpert Contact Form" <${process.env.EMAIL_FROM}>`,
      to: process.env.EMAIL_TO || '<EMAIL>',
      subject: `New Contact Form Submission - ${fullName}`,
      html: createEmailTemplate({ fullName, email, companyName, phone, projectRequirements }),
      // Also send a plain text version
      text: `
        New Contact Form Submission from CottonXpert Website
        
        Name: ${fullName}
        Email: ${email}
        ${companyName ? `Company: ${companyName}` : ''}
        ${phone ? `Phone: ${phone}` : ''}
        
        Project Requirements:
        ${projectRequirements}
        
        Submitted: ${new Date().toLocaleString()}
      `
    };
    
    // Send email
    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', info.messageId);
    
    res.json({
      success: true,
      message: 'Email sent successfully!',
      messageId: info.messageId
    });
    
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send email',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'CottonXpert Email Server'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Email server running on port ${PORT}`);
  console.log(`📧 Email service: ${process.env.EMAIL_HOST}`);
  console.log(`📬 Sending emails to: ${process.env.EMAIL_TO}`);
  console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL}`);
});

module.exports = app;
