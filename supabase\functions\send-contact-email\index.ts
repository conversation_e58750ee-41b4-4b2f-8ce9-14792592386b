import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { record } = await req.json()
    
    // Email content
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50; margin: 0;">🧵 CottonXpert</h1>
            <p style="color: #7f8c8d; margin: 5px 0 0 0;">New Contact Form Submission</p>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #2c3e50; margin-top: 0;">Contact Details</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #34495e; width: 120px;">Name:</td>
                <td style="padding: 8px 0; color: #2c3e50;">${record.full_name}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #34495e;">Email:</td>
                <td style="padding: 8px 0; color: #2c3e50;">
                  <a href="mailto:${record.email}" style="color: #3498db; text-decoration: none;">${record.email}</a>
                </td>
              </tr>
              ${record.company_name ? `
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #34495e;">Company:</td>
                <td style="padding: 8px 0; color: #2c3e50;">${record.company_name}</td>
              </tr>
              ` : ''}
              ${record.phone ? `
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #34495e;">Phone:</td>
                <td style="padding: 8px 0; color: #2c3e50;">
                  <a href="tel:${record.phone}" style="color: #3498db; text-decoration: none;">${record.phone}</a>
                </td>
              </tr>
              ` : ''}
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #34495e;">Date:</td>
                <td style="padding: 8px 0; color: #2c3e50;">${new Date(record.created_at).toLocaleString()}</td>
              </tr>
            </table>
          </div>
          
          <div style="background-color: #e8f4f8; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
            <h3 style="color: #2c3e50; margin-top: 0;">Project Requirements</h3>
            <p style="color: #2c3e50; line-height: 1.6; margin-bottom: 0;">${record.project_requirements}</p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ecf0f1;">
            <p style="color: #7f8c8d; font-size: 14px; margin: 0;">
              This email was sent automatically from your CottonXpert website contact form.
            </p>
            <p style="color: #7f8c8d; font-size: 12px; margin: 10px 0 0 0;">
              Visit your <a href="${req.headers.get('origin')}/admin" style="color: #3498db;">admin dashboard</a> to manage messages.
            </p>
          </div>
        </div>
      </div>
    `

    // Send email using Resend
    const res = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify({
        from: 'CottonXpert <<EMAIL>>',
        to: ['<EMAIL>'],
        subject: `New Contact Form Submission - ${record.full_name}`,
        html: emailHtml,
      }),
    })

    if (res.ok) {
      const data = await res.json()
      return new Response(JSON.stringify(data), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    } else {
      const error = await res.text()
      return new Response(JSON.stringify({ error }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
