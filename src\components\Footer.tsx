import { Globe, Mail, Phone, MapPin } from "lucide-react";
import { Link } from "react-router-dom";

export function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <Globe className="h-8 w-8 text-textile-warm" />
              <div>
                <h3 className="text-2xl font-bold">CottonXpert</h3>
                <p className="text-sm text-primary-foreground/80">Textile Sourcing & Inspection</p>
              </div>
            </div>
            <p className="text-primary-foreground/80 mb-4 max-w-md">
              Your trusted partner for premium textile sourcing and quality inspection services. 
              Serving institutional and retail clients worldwide for over 20 years.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <Mail className="h-4 w-4 text-textile-warm" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Phone className="h-4 w-4 text-textile-warm" />
                <span>+92-321-8765432</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <MapPin className="h-4 w-4 text-textile-warm" />
                <span>Karachi, Pakistan</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Our Products</h4>
            <ul className="space-y-2 text-sm text-primary-foreground/80">
              <li><a href="#" className="hover:text-textile-warm transition-colors">Terry Linen</a></li>
              <li><a href="#" className="hover:text-textile-warm transition-colors">Bed Linen</a></li>
              <li><a href="#" className="hover:text-textile-warm transition-colors">Blankets & Throws</a></li>
              <li><a href="#" className="hover:text-textile-warm transition-colors">Uniforms</a></li>
              <li><a href="#" className="hover:text-textile-warm transition-colors">Fabrics & Materials</a></li>
              <li><a href="#" className="hover:text-textile-warm transition-colors">Custom Garments</a></li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Partnership</h4>
            <div className="space-y-4">
              <div>
                <h5 className="font-medium text-textile-warm">Omnia Ventures Limited</h5>
                <p className="text-sm text-primary-foreground/80">United Kingdom Partner</p>
                <p className="text-sm text-primary-foreground/80">+44-20-7123-4567</p>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-8 pt-8">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <p className="text-sm text-primary-foreground/60 text-center sm:text-left">
              © 2024 CottonXpert. All rights reserved. | Partnership with Omnia Ventures Limited, UK
            </p>
            <Link
              to="/admin"
              className="text-xs text-primary-foreground/40 hover:text-textile-warm transition-colors"
            >
              Admin Portal
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}