import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { X, Star, CheckCircle, Truck, Shield, Award, Mail } from "lucide-react";

interface ProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: {
    title: string;
    description: string;
    image: string;
    category: string;
    features: string[];
  } | null;
}

const productDetails = {
  "Terry Linen": {
    fullDescription: "Our premium terry linen collection represents the pinnacle of absorbent textile manufacturing. Crafted from 100% pure cotton, these products are designed for the demanding requirements of hospitality and healthcare sectors.",
    specifications: [
      "Weight: 400-600 GSM",
      "Composition: 100% Ring Spun Cotton",
      "Weave: Terry Loop Construction",
      "Colors: White, Ivory, and Custom Colors",
      "Sizes: Standard and Custom Dimensions"
    ],
    benefits: [
      "Superior Absorbency",
      "Quick Drying Properties",
      "Lint-Free Performance",
      "Commercial Grade Durability",
      "Eco-Friendly Manufacturing"
    ],
    applications: [
      "5-Star Hotels & Resorts",
      "Hospitals & Healthcare Facilities",
      "Spas & Wellness Centers",
      "Luxury Retail Chains",
      "Corporate Housing"
    ],
    certifications: ["OEKO-TEX Standard 100", "ISO 9001:2015", "GOTS Certified"]
  },
  "Bed Linen": {
    fullDescription: "Experience luxury with our premium bed linen collection, featuring thread counts from 200 to 800. Each piece is meticulously crafted to provide the perfect balance of comfort, durability, and elegance.",
    specifications: [
      "Thread Count: 200-800 TC",
      "Fabric: Percale & Sateen Weaves",
      "Composition: 100% Cotton or Cotton Blends",
      "Finishing: Mercerized for Shine",
      "Packaging: Individual Poly Bags"
    ],
    benefits: [
      "Wrinkle Resistant Finish",
      "Color Fast Technology",
      "Breathable Comfort",
      "Easy Care Instructions",
      "Long-Lasting Quality"
    ],
    applications: [
      "Boutique Hotels",
      "Luxury Accommodations",
      "High-End Retail",
      "Corporate Lodging",
      "Premium Home Collections"
    ],
    certifications: ["Better Cotton Initiative", "STANDARD 100 by OEKO-TEX", "Fair Trade Certified"]
  },
  "Blankets & Throws": {
    fullDescription: "Our blanket and throw collection combines comfort with style, offering various weights and textures to suit different climates and preferences. From lightweight summer throws to heavy winter blankets.",
    specifications: [
      "Weight Options: 300-1200 GSM",
      "Materials: Cotton, Wool, Acrylic Blends",
      "Sizes: Twin to King Size",
      "Edge Finishing: Hemmed or Fringed",
      "Care: Machine Washable Options"
    ],
    benefits: [
      "Hypoallergenic Materials",
      "Temperature Regulation",
      "Fade Resistant Colors",
      "Anti-Pilling Treatment",
      "Sustainable Sourcing"
    ],
    applications: [
      "Hotel Guest Rooms",
      "Airlines & Transportation",
      "Healthcare Facilities",
      "Retail Home Goods",
      "Corporate Gifts"
    ],
    certifications: ["Global Recycled Standard", "Cradle to Cradle Certified", "GREENGUARD Gold"]
  },
  "Uniforms": {
    fullDescription: "Professional uniforms designed for durability and comfort in demanding work environments. Our uniform collection meets industry standards while maintaining a professional appearance throughout extended use.",
    specifications: [
      "Fabric Weight: 180-280 GSM",
      "Blend Options: Cotton/Polyester Mixes",
      "Color Fastness: Grade 4-5",
      "Shrinkage: <3% After Washing",
      "Customization: Embroidery & Printing"
    ],
    benefits: [
      "Stain Resistant Treatments",
      "Antimicrobial Properties",
      "Moisture Wicking Technology",
      "Reinforced Stress Points",
      "Professional Appearance"
    ],
    applications: [
      "Healthcare Professionals",
      "Hospitality Staff",
      "Corporate Workwear",
      "Industrial Uniforms",
      "Service Industries"
    ],
    certifications: ["ISO 14001", "WRAP Certified", "SA8000 Social Accountability"]
  },
  "Fabrics & Materials": {
    fullDescription: "Raw materials and specialty fabrics sourced from certified mills worldwide. We provide bulk fabric solutions for manufacturers, designers, and large-scale textile operations.",
    specifications: [
      "Fabric Types: Woven & Knitted",
      "Fiber Content: Natural & Synthetic",
      "Width: 44\" to 118\" Available",
      "Minimum Order: 1000 Meters",
      "Lead Time: 15-45 Days"
    ],
    benefits: [
      "Competitive Bulk Pricing",
      "Quality Assurance Testing",
      "Global Sourcing Network",
      "Custom Development",
      "Technical Support"
    ],
    applications: [
      "Garment Manufacturing",
      "Home Textile Production",
      "Industrial Applications",
      "Fashion Design Houses",
      "Textile Converters"
    ],
    certifications: ["GRS Certified", "BCI Cotton", "Lenzing EcoVero"]
  }
};

export function ProductModal({ isOpen, onClose, product }: ProductModalProps) {
  if (!product) return null;

  const details = productDetails[product.title as keyof typeof productDetails];

  const scrollToContact = () => {
    onClose();
    setTimeout(() => {
      const contactElement = document.getElementById('contact');
      if (contactElement) {
        contactElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-2xl font-bold text-primary">{product.title}</span>
              <Badge variant="secondary">{product.category}</Badge>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Product Image */}
          <div className="space-y-4">
            <div className="relative overflow-hidden rounded-lg">
              <img 
                src={product.image} 
                alt={product.title}
                className="w-full h-64 lg:h-80 object-cover"
              />
              <div className="absolute top-4 right-4">
                <Badge className="bg-textile-warm text-white">Premium Quality</Badge>
              </div>
            </div>
            
            <Card>
              <CardContent className="p-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Award className="h-4 w-4 text-textile-warm" />
                  Certifications
                </h4>
                <div className="flex flex-wrap gap-2">
                  {details?.certifications.map((cert, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {cert}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <p className="text-muted-foreground leading-relaxed">
                {details?.fullDescription || product.description}
              </p>
            </div>

            {/* Specifications */}
            <Card>
              <CardContent className="p-4">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Specifications
                </h4>
                <ul className="space-y-2">
                  {details?.specifications.map((spec, idx) => (
                    <li key={idx} className="text-sm flex items-start gap-2">
                      <span className="w-2 h-2 bg-textile-warm rounded-full mt-2 flex-shrink-0"></span>
                      {spec}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card>
              <CardContent className="p-4">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Star className="h-4 w-4 text-yellow-500" />
                  Key Benefits
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {details?.benefits.map((benefit, idx) => (
                    <div key={idx} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                      {benefit}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Applications */}
            <Card>
              <CardContent className="p-4">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Truck className="h-4 w-4 text-blue-600" />
                  Applications
                </h4>
                <div className="flex flex-wrap gap-2">
                  {details?.applications.map((app, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {app}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={scrollToContact}
                className="flex-1 bg-textile-warm hover:bg-textile-warm/90"
              >
                <Mail className="h-4 w-4 mr-2" />
                Request Quote
              </Button>
              <Button 
                variant="outline" 
                onClick={scrollToContact}
                className="flex-1"
              >
                <Shield className="h-4 w-4 mr-2" />
                Quality Inspection
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
