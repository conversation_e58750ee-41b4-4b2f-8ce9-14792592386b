@echo off
echo 🧵 Starting CottonXpert Website...
echo.

echo Checking environment configuration...
if not exist .env.local (
    echo ⚠️  Creating .env.local from template...
    copy .env.example .env.local
    echo.
    echo 📧 Email notifications will be configured later
    echo    See EMAIL_SETUP.md for EmailJS setup instructions
    echo.
)

echo.
echo 🚀 Starting development server...
npm run dev

echo.
echo ✅ CottonXpert is running!
echo.
echo 🌐 Website: http://localhost:8080
echo 📋 Product Catalog: http://localhost:8080/catalog
echo.
echo 📧 CONTACT FORM SETUP:
echo   1. Test the contact form on the main website
echo   2. Messages will show success but won't send emails yet
echo   3. Follow EMAIL_SETUP.md to configure EmailJS
echo   4. Once configured, messages will be <NAME_EMAIL>
echo.
echo 📖 Email Setup Guide: EMAIL_SETUP.md
echo.
echo ✨ Features:
echo   ✅ Responsive Website Design
echo   ✅ Product Showcase with Modals
echo   ✅ Contact Form (ready for email setup)
echo   ✅ Mobile-Friendly Navigation
echo   ✅ Professional UI/UX
echo.
pause
