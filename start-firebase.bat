@echo off
echo 🔥 Starting <PERSON><PERSON><PERSON> with Firebase...
echo.

echo Checking environment configuration...
if not exist .env.local (
    echo ⚠️  Creating .env.local from template...
    copy .env.example .env.local
    echo.
    echo 🚨 IMPORTANT: Please configure your Firebase settings in .env.local
    echo    1. Go to Firebase Console: https://console.firebase.google.com/
    echo    2. Create a new project or select existing one
    echo    3. Get your web app configuration
    echo    4. Update .env.local with your Firebase config
    echo.
    echo Press any key to continue after configuring Firebase...
    pause
)

echo.
echo 🚀 Starting development server...
npm run dev

echo.
echo ✅ CottonXpert is running!
echo.
echo 🌐 Website: http://localhost:8080
echo 👨‍💼 Admin Portal: http://localhost:8080/admin
echo 📋 Product Catalog: http://localhost:8080/catalog
echo.
echo 🔒 Security Features:
echo   ✅ Firebase Authentication
echo   ✅ Firestore Security Rules
echo   ✅ Environment Variables Protection
echo   ✅ Input Validation & Sanitization
echo   ✅ Rate Limiting
echo.
echo 📧 To receive email notifications:
echo   1. Set up Firebase Functions (see firebase-setup.md)
echo   2. Or use a third-party service like EmailJS
echo.
pause
