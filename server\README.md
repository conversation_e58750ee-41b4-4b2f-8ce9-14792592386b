# CottonXpert Email Server

A Node.js email server using nodemail<PERSON> and SM<PERSON> for handling contact form submissions.

## Quick Setup

### 1. Install Dependencies
```bash
cd server
npm install
```

### 2. Configure Environment Variables
```bash
# Copy the example file
cp .env.example .env

# Edit .env with your email settings
nano .env
```

### 3. Gmail Setup (Recommended)
1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password:**
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
   - Use this password in EMAIL_PASS

### 4. Environment Configuration
Edit `.env` file:
```env
# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-16-character-app-password
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>

# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:8080
```

### 5. Start the Server
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

### 6. Test Email Configuration
```bash
# Test if email server is working
curl http://localhost:3001/api/test-email
```

## API Endpoints

### POST /api/send-email
Send contact form email notification.

**Request Body:**
```json
{
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "companyName": "ABC Corp",
  "phone": "+**********",
  "projectRequirements": "Need 100 terry towels..."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email sent successfully!",
  "messageId": "message-id-from-smtp"
}
```

### GET /api/test-email
Test email server configuration.

### GET /api/health
Health check endpoint.

## Email Providers

### Gmail (Recommended)
- Host: smtp.gmail.com
- Port: 587
- Requires App Password

### Outlook/Hotmail
- Host: smtp-mail.outlook.com
- Port: 587
- Use regular password

### Yahoo
- Host: smtp.mail.yahoo.com
- Port: 587
- Requires App Password

### Custom SMTP
Update EMAIL_HOST and EMAIL_PORT in .env

## Troubleshooting

### Common Issues:

1. **"Invalid login" error:**
   - Use App Password for Gmail
   - Enable "Less secure app access" for other providers

2. **Connection timeout:**
   - Check firewall settings
   - Verify SMTP host and port

3. **CORS errors:**
   - Update FRONTEND_URL in .env
   - Check if frontend is running on correct port

4. **Email not received:**
   - Check spam folder
   - Verify EMAIL_TO address
   - Check email provider logs

### Testing Steps:

1. **Test server connection:**
   ```bash
   curl http://localhost:3001/api/health
   ```

2. **Test email configuration:**
   ```bash
   curl http://localhost:3001/api/test-email
   ```

3. **Test email sending:**
   ```bash
   curl -X POST http://localhost:3001/api/send-email \
     -H "Content-Type: application/json" \
     -d '{
       "fullName": "Test User",
       "email": "<EMAIL>",
       "projectRequirements": "This is a test message"
     }'
   ```

## Production Deployment

### Environment Variables for Production:
```env
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://your-domain.com
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_TO=<EMAIL>
```

### PM2 Deployment:
```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start server.js --name "cottonxpert-email"

# Save PM2 configuration
pm2 save
pm2 startup
```

### Docker Deployment:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

## Security Notes

- Never commit .env file to version control
- Use App Passwords instead of regular passwords
- Enable 2FA on email accounts
- Use HTTPS in production
- Implement rate limiting for production use

## Support

If you encounter issues:
1. Check the server logs
2. Verify email provider settings
3. Test with curl commands
4. Check firewall and network settings
