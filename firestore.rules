rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Contact messages collection
    match /contact_messages/{messageId} {
      // Allow anyone to create contact messages (public contact form)
      allow create: if isValidContactMessage(resource.data);
      
      // Only authenticated admin users can read, update, or delete
      allow read, update, delete: if isAuthenticated() && isAdmin();
    }
    
    // Admin users collection (for authentication)
    match /admin_users/{userId} {
      // Only authenticated users can read their own data
      allow read: if isAuthenticated() && request.auth.uid == userId;
      
      // Only existing admins can create new admin users
      allow create, update, delete: if isAuthenticated() && isAdmin();
    }
    
    // Helper functions for security
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      // Check if user exists in admin_users collection
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/admin_users/$(request.auth.uid));
    }
    
    function isValidContactMessage(data) {
      // Validate required fields and data types
      return data.keys().hasAll(['full_name', 'email', 'project_requirements', 'created_at']) &&
             data.full_name is string &&
             data.full_name.size() > 0 &&
             data.full_name.size() <= 100 &&
             data.email is string &&
             data.email.matches('.*@.*\\..*') && // Basic email validation
             data.email.size() <= 254 &&
             data.project_requirements is string &&
             data.project_requirements.size() > 0 &&
             data.project_requirements.size() <= 5000 &&
             data.created_at is timestamp &&
             // Optional fields validation
             (!data.keys().hasAny(['company_name']) || 
              (data.company_name is string && data.company_name.size() <= 100)) &&
             (!data.keys().hasAny(['phone']) || 
              (data.phone is string && data.phone.size() <= 20)) &&
             // Prevent XSS and injection attacks
             !data.full_name.matches('.*<.*>.*') &&
             !data.email.matches('.*<.*>.*') &&
             !data.project_requirements.matches('.*<script.*') &&
             // Rate limiting: prevent spam (basic check)
             data.created_at > timestamp.date(2024, 1, 1);
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
