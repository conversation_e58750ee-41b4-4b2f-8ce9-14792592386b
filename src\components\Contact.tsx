import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, Phone, MapPin, Clock, Send } from "lucide-react";
import { contactMessageService, type ContactMessageInput } from "@/lib/firestore";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";

export function Contact() {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    companyName: "",
    phone: "",
    projectRequirements: ""
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.fullName || !formData.email || !formData.projectRequirements) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('🚀 Starting contact form submission...');

      // Save to Firebase Firestore
      const contactData: ContactMessageInput = {
        full_name: formData.fullName.trim(),
        email: formData.email.trim().toLowerCase(),
        company_name: formData.companyName?.trim() || undefined,
        phone: formData.phone?.trim() || undefined,
        project_requirements: formData.projectRequirements.trim()
      };

      console.log('📝 Contact data prepared:', contactData);

      const result = await contactMessageService.create(contactData);

      console.log('🔥 Firebase result:', result);

      if (!result.success) {
        throw new Error(result.error || 'Failed to save message');
      }

      console.log('✅ Message saved to Firebase successfully!');

      // Email notification (optional - will be set up later)
      console.log('📧 Email notification: Not configured yet (Firebase Functions needed)');
      console.log('💡 To set up email notifications, see firebase-setup.md');

      toast({
        title: "Message Sent!",
        description: "Thank you for contacting us. We'll get back to you within 24 hours."
      });

      // Reset form
      setFormData({
        fullName: "",
        email: "",
        companyName: "",
        phone: "",
        projectRequirements: ""
      });

    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 bg-gradient-section">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-textile-warm/10 text-textile-warm border-textile-warm/20">
            Get In Touch
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-primary">
            Ready to Start Your Project?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Contact our team of textile experts to discuss your sourcing and inspection needs. 
            We're here to help you find the perfect textile solutions.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="shadow-card-hover">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold mb-6 text-primary">Send us a Message</h3>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-foreground">
                        Full Name *
                      </label>
                      <Input 
                        placeholder="Your full name" 
                        value={formData.fullName}
                        onChange={(e) => handleInputChange('fullName', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2 text-foreground">
                        Email Address *
                      </label>
                      <Input 
                        type="email" 
                        placeholder="<EMAIL>" 
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-foreground">
                        Company Name
                      </label>
                      <Input 
                        placeholder="Your company name" 
                        value={formData.companyName}
                        onChange={(e) => handleInputChange('companyName', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2 text-foreground">
                        Phone Number
                      </label>
                      <Input 
                        placeholder="+****************" 
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2 text-foreground">
                      Project Requirements *
                    </label>
                    <Textarea 
                      placeholder="Please describe your textile sourcing or inspection needs, including product types, quantities, and timeline..."
                      rows={5}
                      value={formData.projectRequirements}
                      onChange={(e) => handleInputChange('projectRequirements', e.target.value)}
                      required
                    />
                  </div>

                  <Button 
                    type="submit" 
                    variant="premium" 
                    size="lg" 
                    className="w-full group" 
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Sending..." : "Send Message"}
                    <Send className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="hover:shadow-card-hover transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Mail className="h-6 w-6 text-textile-warm mr-3" />
                  <h4 className="text-lg font-semibold text-primary">Email Us</h4>
                </div>
                <p className="text-muted-foreground mb-2">General Inquiries:</p>
                <p className="font-medium text-foreground"><EMAIL></p>
                <p className="text-muted-foreground mb-2 mt-3">Sales Department:</p>
                <p className="font-medium text-foreground"><EMAIL></p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-card-hover transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Phone className="h-6 w-6 text-textile-warm mr-3" />
                  <h4 className="text-lg font-semibold text-primary">Call Us</h4>
                </div>
                <p className="text-muted-foreground mb-2">Pakistan Office:</p>
                <p className="font-medium text-foreground">+92-321-8765432</p>
                <p className="text-muted-foreground mb-2 mt-3">UK Partner:</p>
                <p className="font-medium text-foreground">+44-20-7123-4567</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-card-hover transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <MapPin className="h-6 w-6 text-textile-warm mr-3" />
                  <h4 className="text-lg font-semibold text-primary">Visit Us</h4>
                </div>
                <p className="text-muted-foreground mb-2">Headquarters:</p>
                <p className="font-medium text-foreground">Karachi, Pakistan</p>
                <p className="text-muted-foreground mb-2 mt-3">Partner Office:</p>
                <p className="font-medium text-foreground">London, United Kingdom</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-card-hover transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Clock className="h-6 w-6 text-textile-warm mr-3" />
                  <h4 className="text-lg font-semibold text-primary">Business Hours</h4>
                </div>
                <p className="text-muted-foreground mb-2">Monday - Friday:</p>
                <p className="font-medium text-foreground">9:00 AM - 6:00 PM PST</p>
                <p className="text-muted-foreground mb-2 mt-3">Response Time:</p>
                <p className="font-medium text-foreground">Within 24 hours</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}