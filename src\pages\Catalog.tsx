import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ArrowLeft, Search, Filter, Download, Eye, Mail, Phone } from "lucide-react";
import { Link } from "react-router-dom";
import { ProductModal } from "@/components/ProductModal";

import terryLinen from "@/assets/terry-linen.jpg";
import bedLinen from "@/assets/bed-linen.jpg";
import blankets from "@/assets/blankets.jpg";
import uniforms from "@/assets/uniforms.jpg";
import fabrics from "@/assets/fabrics.jpg";

const catalogProducts = [
  {
    title: "Terry Linen",
    description: "Premium quality terry towels and bathrobes for hospitality and healthcare sectors.",
    image: terryLinen,
    category: "Hotel & Healthcare",
    features: ["100% Cotton", "Absorbent", "Durable"],
    price: "Starting from $12/piece",
    minOrder: "100 pieces",
    leadTime: "15-20 days"
  },
  {
    title: "Bed Linen",
    description: "Luxury bed sheets, pillowcases, and duvet covers for hotels and retail.",
    image: bedLinen,
    category: "Hospitality",
    features: ["Thread Count 200-800", "Wrinkle Free", "Color Fast"],
    price: "Starting from $25/set",
    minOrder: "50 sets",
    leadTime: "20-25 days"
  },
  {
    title: "Blankets & Throws",
    description: "Cozy blankets and decorative throws for comfort and style.",
    image: blankets,
    category: "Comfort",
    features: ["Multiple Weights", "Hypoallergenic", "Machine Washable"],
    price: "Starting from $18/piece",
    minOrder: "75 pieces",
    leadTime: "12-18 days"
  },
  {
    title: "Uniforms",
    description: "Professional uniforms for healthcare, hospitality, and corporate sectors.",
    image: uniforms,
    category: "Professional",
    features: ["Custom Design", "Durable Fabric", "Easy Care"],
    price: "Starting from $35/set",
    minOrder: "25 sets",
    leadTime: "25-30 days"
  },
  {
    title: "Fabrics & Materials",
    description: "Raw materials and fabric supplies for textile manufacturing.",
    image: fabrics,
    category: "Manufacturing",
    features: ["Bulk Orders", "Quality Tested", "Global Sourcing"],
    price: "Starting from $8/meter",
    minOrder: "1000 meters",
    leadTime: "30-45 days"
  },
  // Additional products for a complete catalog
  {
    title: "Hotel Curtains",
    description: "Blackout and decorative curtains for hospitality industry.",
    image: fabrics,
    category: "Hotel & Healthcare",
    features: ["Blackout Options", "Fire Retardant", "Custom Sizes"],
    price: "Starting from $45/panel",
    minOrder: "20 panels",
    leadTime: "20-25 days"
  },
  {
    title: "Table Linen",
    description: "Restaurant and banquet table cloths, napkins, and runners.",
    image: bedLinen,
    category: "Hospitality",
    features: ["Stain Resistant", "Various Sizes", "Color Options"],
    price: "Starting from $15/piece",
    minOrder: "50 pieces",
    leadTime: "15-20 days"
  },
  {
    title: "Medical Textiles",
    description: "Specialized textiles for healthcare and medical applications.",
    image: uniforms,
    category: "Healthcare",
    features: ["Antimicrobial", "Fluid Resistant", "Comfortable"],
    price: "Starting from $22/piece",
    minOrder: "100 pieces",
    leadTime: "20-30 days"
  }
];

export default function Catalog() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedProduct, setSelectedProduct] = useState<typeof catalogProducts[0] | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const categories = ["all", ...Array.from(new Set(catalogProducts.map(p => p.category)))];

  const filteredProducts = catalogProducts.filter(product => {
    const matchesSearch = product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleLearnMore = (product: typeof catalogProducts[0]) => {
    setSelectedProduct(product);
    setIsModalOpen(true);
  };

  const scrollToContact = () => {
    window.location.href = "/#contact";
  };

  const downloadCatalog = () => {
    // In a real application, this would download a PDF catalog
    alert("PDF catalog download will be available soon. Please contact us for a detailed catalog.");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-6 sm:py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-primary flex items-center gap-2">
              📋 Complete Product Catalog
            </h1>
            <p className="text-muted-foreground mt-1">Browse our full range of textile products</p>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              onClick={downloadCatalog} 
              variant="outline" 
              size="sm"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Download PDF</span>
            </Button>
            <Link to="/">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Back to Home</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Search and Filter */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === "all" ? "All Categories" : category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-3 text-sm text-muted-foreground">
              Showing {filteredProducts.length} of {catalogProducts.length} products
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {filteredProducts.map((product, index) => (
            <Card 
              key={`${product.title}-${index}`}
              className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-card to-card/80"
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img 
                    src={product.image} 
                    alt={product.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <Button 
                    size="icon"
                    variant="ghost"
                    className="absolute top-2 right-2 bg-white/90 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleLearnMore(product)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="secondary" className="text-xs">
                      {product.category}
                    </Badge>
                    <span className="text-sm font-semibold text-textile-warm">
                      {product.price}
                    </span>
                  </div>
                  
                  <h3 className="text-lg font-semibold mb-2 text-primary">
                    {product.title}
                  </h3>
                  
                  <p className="text-muted-foreground mb-3 text-sm">
                    {product.description}
                  </p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Min Order:</span>
                      <span className="font-medium">{product.minOrder}</span>
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Lead Time:</span>
                      <span className="font-medium">{product.leadTime}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="flex-1 group/btn"
                      onClick={() => handleLearnMore(product)}
                    >
                      Learn More
                      <Eye className="ml-2 h-3 w-3 group-hover/btn:scale-110 transition-transform" />
                    </Button>
                    <Button 
                      size="sm" 
                      className="bg-textile-warm hover:bg-textile-warm/90"
                      onClick={scrollToContact}
                    >
                      Quote
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact CTA */}
        <Card className="bg-gradient-to-r from-textile-warm/10 to-textile-earth/10 border-textile-warm/20">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4 text-primary">Need Custom Solutions?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Can't find exactly what you're looking for? We specialize in custom textile solutions 
              tailored to your specific requirements. Contact us for personalized quotes and samples.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={scrollToContact}
                className="bg-textile-warm hover:bg-textile-warm/90"
              >
                <Mail className="h-4 w-4 mr-2" />
                Get Custom Quote
              </Button>
              <Button variant="outline">
                <Phone className="h-4 w-4 mr-2" />
                Call +92-321-8765432
              </Button>
            </div>
          </CardContent>
        </Card>

        <ProductModal 
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          product={selectedProduct}
        />
      </div>
    </div>
  );
}
