import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { contactMessageService, type ContactMessage as FirebaseContactMessage } from "@/lib/firestore";
import { Timestamp } from "firebase/firestore";
import { authService } from "@/lib/auth";
import { AdminLogin } from "@/components/AdminLogin";
import { useToast } from "@/hooks/use-toast";
import {
  Mail,
  Phone,
  Building,
  Calendar,
  MessageSquare,
  ArrowLeft,
  Trash2,
  Search,
  Filter,
  Download,
  RefreshCw,
  Users,
  TrendingUp,
  Clock
} from "lucide-react";
import { Link } from "react-router-dom";

// Use Firebase ContactMessage type
type ContactMessage = FirebaseContactMessage;

export default function Admin() {
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [filteredMessages, setFilteredMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authLoading, setAuthLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Check authentication status
    const unsubscribe = authService.onAuthStateChanged(async (user) => {
      if (user) {
        const isAdmin = await authService.isUserAdmin(user.uid);
        setIsAuthenticated(isAdmin);
        if (isAdmin) {
          fetchMessages();
        }
      } else {
        setIsAuthenticated(false);
      }
      setAuthLoading(false);
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    filterMessages();
  }, [messages, searchTerm, filterBy]);

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const result = await contactMessageService.getAll();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch messages');
      }

      setMessages(result.data || []);
      toast({
        title: "Success",
        description: "Messages refreshed successfully."
      });
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast({
        title: "Error",
        description: "Failed to fetch contact messages.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filterMessages = () => {
    let filtered = messages;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(msg =>
        msg.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msg.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msg.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msg.project_requirements.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (filterBy !== "all") {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      filtered = filtered.filter(msg => {
        const msgDate = msg.created_at.toDate();
        switch (filterBy) {
          case "today":
            return msgDate >= today;
          case "week":
            return msgDate >= thisWeek;
          case "month":
            return msgDate >= thisMonth;
          case "company":
            return msg.company_name !== undefined && msg.company_name !== null;
          default:
            return true;
        }
      });
    }

    setFilteredMessages(filtered);
  };

  const deleteMessage = async (id: string) => {
    try {
      const result = await contactMessageService.delete(id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete message');
      }

      setMessages(messages.filter(msg => msg.id !== id));
      toast({
        title: "Success",
        description: "Message deleted successfully."
      });
    } catch (error) {
      console.error('Error deleting message:', error);
      toast({
        title: "Error",
        description: "Failed to delete message.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (timestamp: Timestamp) => {
    return timestamp.toDate().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeAgo = (timestamp: Timestamp) => {
    const now = new Date();
    const msgDate = timestamp.toDate();
    const diffInHours = Math.floor((now.getTime() - msgDate.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return formatDate(timestamp);
  };

  const exportToCSV = () => {
    const csvContent = [
      ['Name', 'Email', 'Company', 'Phone', 'Requirements', 'Date'],
      ...filteredMessages.map(msg => [
        msg.full_name,
        msg.email,
        msg.company_name || '',
        msg.phone || '',
        msg.project_requirements.replace(/,/g, ';'),
        formatDate(msg.created_at)
      ])
    ].map(row => row.map(field => `"${field}"`).join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `contact-messages-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleLogout = async () => {
    try {
      await authService.signOut();
      toast({
        title: "Logged Out",
        description: "You have been successfully logged out."
      });
    } catch (error) {
      toast({
        title: "Logout Error",
        description: "Failed to log out. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-textile-warm mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <AdminLogin onLoginSuccess={() => setIsAuthenticated(true)} />;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-textile-warm mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-6 sm:py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-primary flex items-center gap-2">
              <MessageSquare className="h-6 w-6 sm:h-8 sm:w-8 text-textile-warm" />
              Admin Dashboard
            </h1>
            <p className="text-muted-foreground mt-1">Manage contact form submissions</p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={exportToCSV}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              disabled={filteredMessages.length === 0}
            >
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Export CSV</span>
            </Button>
            <Button
              onClick={handleLogout}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Logout</span>
            </Button>
            <Link to="/">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Back to Website</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border-blue-200 dark:border-blue-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Total Messages</CardTitle>
              <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{messages.length}</div>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">All time</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 border-green-200 dark:border-green-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">This Month</CardTitle>
              <Calendar className="h-4 w-4 text-green-600 dark:text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                {messages.filter(msg =>
                  msg.created_at.toDate().getMonth() === new Date().getMonth()
                ).length}
              </div>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">Recent activity</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 border-purple-200 dark:border-purple-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">With Company</CardTitle>
              <Building className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                {messages.filter(msg => msg.company_name).length}
              </div>
              <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">Business inquiries</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 border-orange-200 dark:border-orange-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Today</CardTitle>
              <Clock className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                {messages.filter(msg => {
                  const today = new Date();
                  const msgDate = msg.created_at.toDate();
                  return msgDate.toDateString() === today.toDateString();
                }).length}
              </div>
              <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">New today</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search messages by name, email, company, or requirements..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  className="px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="all">All Messages</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="company">With Company</option>
                </select>
                <Button
                  onClick={fetchMessages}
                  variant="outline"
                  size="sm"
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  <span className="hidden sm:inline">Refresh</span>
                </Button>
              </div>
            </div>
            <div className="mt-3 flex items-center justify-between text-sm text-muted-foreground">
              <span>
                Showing {filteredMessages.length} of {messages.length} messages
              </span>
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchTerm("")}
                  className="h-auto p-1 text-xs"
                >
                  Clear search
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <Users className="h-5 w-5 text-textile-warm" />
              Contact Messages
            </h2>
          </div>

          {filteredMessages.length === 0 ? (
            <Card className="bg-gradient-to-br from-muted/20 to-muted/40">
              <CardContent className="p-8 text-center">
                <MessageSquare className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {messages.length === 0 ? "No messages yet" : "No messages match your search"}
                </h3>
                <p className="text-muted-foreground">
                  {messages.length === 0
                    ? "Contact form submissions will appear here."
                    : "Try adjusting your search terms or filters."
                  }
                </p>
                {searchTerm && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSearchTerm("")}
                    className="mt-4"
                  >
                    Clear search
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            filteredMessages.map((message) => (
              <Card key={message.id} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-card to-card/80 border-l-4 border-l-textile-warm">
                <CardHeader className="pb-3">
                  <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-lg text-primary">{message.full_name}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          {getTimeAgo(message.created_at)}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Mail className="h-3 w-3 text-textile-warm" />
                          <span className="truncate">{message.email}</span>
                        </div>
                        {message.phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-3 w-3 text-textile-warm" />
                            <span>{message.phone}</span>
                          </div>
                        )}
                      </div>

                      {message.company_name && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Building className="h-3 w-3 text-textile-warm" />
                          <span className="font-medium">{message.company_name}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2 self-start">
                      <Badge variant="secondary" className="text-xs whitespace-nowrap">
                        {formatDate(message.created_at)}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteMessage(message.id)}
                        className="text-destructive hover:text-destructive hover:bg-destructive/10 transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-textile-warm" />
                      <h4 className="font-medium text-sm">Project Requirements</h4>
                    </div>
                    <div className="bg-muted/30 rounded-lg p-3 border-l-2 border-l-textile-warm/50">
                      <p className="text-sm leading-relaxed">
                        {message.project_requirements}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
