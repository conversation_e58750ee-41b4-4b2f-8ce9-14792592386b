import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { contactMessageService } from "@/lib/firestore";
import { CheckCircle, XCircle, AlertCircle, TestTube, Database, Send } from "lucide-react";
import { Link } from "react-router-dom";

export default function TestFirebase() {
  const [testResults, setTestResults] = useState<{
    firebaseConnection: 'pending' | 'success' | 'error';
    formSubmission: 'pending' | 'success' | 'error';
    errorMessage?: string;
  }>({
    firebaseConnection: 'pending',
    formSubmission: 'pending'
  });
  
  const [testFormData, setTestFormData] = useState({
    fullName: "Test User",
    email: "<EMAIL>",
    companyName: "Test Company",
    phone: "+1234567890",
    projectRequirements: "This is a test message to verify Firebase integration is working correctly."
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const testFirebaseConnection = async () => {
    try {
      console.log('🔥 Testing Firebase connection...');
      
      // Test basic Firebase connection by attempting to read (will fail gracefully if no permissions)
      const result = await contactMessageService.getAll();
      
      if (result.success || result.error?.includes('permission')) {
        setTestResults(prev => ({ ...prev, firebaseConnection: 'success' }));
        toast({
          title: "Firebase Connected!",
          description: "Firebase connection is working properly."
        });
        return true;
      } else {
        throw new Error(result.error || 'Unknown Firebase error');
      }
    } catch (error: any) {
      console.error('Firebase connection test failed:', error);
      setTestResults(prev => ({ 
        ...prev, 
        firebaseConnection: 'error',
        errorMessage: error.message 
      }));
      toast({
        title: "Firebase Connection Failed",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  };

  const testFormSubmission = async () => {
    setIsLoading(true);
    
    try {
      console.log('📝 Testing form submission...');
      
      const result = await contactMessageService.create({
        full_name: testFormData.fullName,
        email: testFormData.email,
        company_name: testFormData.companyName,
        phone: testFormData.phone,
        project_requirements: testFormData.projectRequirements
      });

      if (result.success) {
        setTestResults(prev => ({ ...prev, formSubmission: 'success' }));
        toast({
          title: "Test Message Sent!",
          description: `Test message saved with ID: ${result.id}`
        });
      } else {
        throw new Error(result.error || 'Form submission failed');
      }
    } catch (error: any) {
      console.error('Form submission test failed:', error);
      setTestResults(prev => ({ 
        ...prev, 
        formSubmission: 'error',
        errorMessage: error.message 
      }));
      toast({
        title: "Form Submission Failed",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runAllTests = async () => {
    setTestResults({
      firebaseConnection: 'pending',
      formSubmission: 'pending'
    });
    
    const connectionSuccess = await testFirebaseConnection();
    
    if (connectionSuccess) {
      await testFormSubmission();
    }
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getStatusText = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return 'Success';
      case 'error':
        return 'Failed';
      default:
        return 'Pending';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 p-4">
      <div className="container mx-auto max-w-4xl">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <TestTube className="h-8 w-8 text-textile-warm" />
            <h1 className="text-3xl font-bold text-primary">Firebase Integration Test</h1>
          </div>
          <p className="text-muted-foreground">
            Test your Firebase connection and contact form functionality
          </p>
          <div className="mt-4">
            <Link to="/">
              <Button variant="outline" size="sm">
                ← Back to Website
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Test Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Test Results
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <span className="font-medium">Firebase Connection</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.firebaseConnection)}
                  <span className="text-sm">{getStatusText(testResults.firebaseConnection)}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <span className="font-medium">Form Submission</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.formSubmission)}
                  <span className="text-sm">{getStatusText(testResults.formSubmission)}</span>
                </div>
              </div>

              {testResults.errorMessage && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-800">
                    <strong>Error:</strong> {testResults.errorMessage}
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <Button 
                  onClick={runAllTests} 
                  className="w-full bg-textile-warm hover:bg-textile-warm/90"
                  disabled={isLoading}
                >
                  {isLoading ? "Running Tests..." : "Run All Tests"}
                </Button>
                
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    onClick={testFirebaseConnection} 
                    variant="outline" 
                    size="sm"
                    disabled={isLoading}
                  >
                    Test Connection
                  </Button>
                  <Button 
                    onClick={testFormSubmission} 
                    variant="outline" 
                    size="sm"
                    disabled={isLoading}
                  >
                    Test Form
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Test Contact Form
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Full Name</label>
                <Input
                  value={testFormData.fullName}
                  onChange={(e) => setTestFormData(prev => ({ ...prev, fullName: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <Input
                  type="email"
                  value={testFormData.email}
                  onChange={(e) => setTestFormData(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Company (Optional)</label>
                <Input
                  value={testFormData.companyName}
                  onChange={(e) => setTestFormData(prev => ({ ...prev, companyName: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Phone (Optional)</label>
                <Input
                  value={testFormData.phone}
                  onChange={(e) => setTestFormData(prev => ({ ...prev, phone: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Project Requirements</label>
                <Textarea
                  value={testFormData.projectRequirements}
                  onChange={(e) => setTestFormData(prev => ({ ...prev, projectRequirements: e.target.value }))}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Troubleshooting</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">If Firebase Connection Fails:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Check .env.local file exists</li>
                  <li>• Verify Firebase config values</li>
                  <li>• Ensure Firestore is enabled</li>
                  <li>• Check browser console for errors</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">If Form Submission Fails:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Deploy Firestore security rules</li>
                  <li>• Check Firebase project permissions</li>
                  <li>• Verify network connectivity</li>
                  <li>• Review browser network tab</li>
                </ul>
              </div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Next Steps:</strong> Once tests pass, go to <code>/admin-setup</code> to create your admin account, 
                then access the admin portal at <code>/admin</code> to view submitted messages.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
