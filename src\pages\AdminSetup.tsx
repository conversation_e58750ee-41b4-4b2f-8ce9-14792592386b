import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { authService } from "@/lib/auth";
import { Eye, EyeOff, UserPlus, Shield, CheckCircle } from "lucide-react";
import { Link } from "react-router-dom";

export default function AdminSetup() {
  const [formData, setFormData] = useState({
    email: "<EMAIL>",
    password: "",
    confirmPassword: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [adminCreated, setAdminCreated] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateSecurePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setFormData(prev => ({ ...prev, password, confirmPassword: password }));
    toast({
      title: "Secure Password Generated",
      description: "A strong password has been generated for you."
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password) {
      toast({
        title: "Missing Information",
        description: "Please enter both email and password.",
        variant: "destructive"
      });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "Passwords do not match.",
        variant: "destructive"
      });
      return;
    }

    if (formData.password.length < 8) {
      toast({
        title: "Weak Password",
        description: "Password must be at least 8 characters long.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await authService.createAdminUser(
        formData.email, 
        formData.password, 
        'super_admin'
      );
      
      if (result.success) {
        setAdminCreated(true);
        toast({
          title: "Admin Created Successfully!",
          description: `Admin user created with email: ${formData.email}`
        });
      } else {
        toast({
          title: "Creation Failed",
          description: result.error || "Failed to create admin user",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Admin creation error:', error);
      toast({
        title: "Creation Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (adminCreated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-lg">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-primary">Admin Created!</CardTitle>
            <p className="text-muted-foreground">Your admin account is ready to use</p>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Your Admin Credentials:</h3>
              <p className="text-sm"><strong>Email:</strong> {formData.email}</p>
              <p className="text-sm"><strong>Password:</strong> {formData.password}</p>
              <p className="text-xs text-muted-foreground mt-2">
                ⚠️ Save these credentials securely!
              </p>
            </div>
            
            <div className="space-y-2">
              <Link to="/admin">
                <Button className="w-full bg-textile-warm hover:bg-textile-warm/90">
                  Go to Admin Portal
                </Button>
              </Link>
              <Link to="/">
                <Button variant="outline" className="w-full">
                  Back to Website
                </Button>
              </Link>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                This setup page will be automatically disabled after first use.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-textile-warm/10 rounded-full">
              <UserPlus className="h-8 w-8 text-textile-warm" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-primary">Create Admin Account</CardTitle>
          <p className="text-muted-foreground">Set up your first admin user for CottonXpert</p>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-foreground">
                Admin Email
              </label>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                autoComplete="email"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2 text-foreground">
                Password
              </label>
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter a secure password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="pr-20"
                  required
                  autoComplete="new-password"
                />
                <div className="absolute right-1 top-1/2 transform -translate-y-1/2 flex gap-1">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              <Button
                type="button"
                variant="link"
                size="sm"
                onClick={generateSecurePassword}
                className="p-0 h-auto text-xs mt-1"
              >
                Generate Secure Password
              </Button>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2 text-foreground">
                Confirm Password
              </label>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                required
                autoComplete="new-password"
              />
            </div>
            
            <Button
              type="submit"
              className="w-full bg-textile-warm hover:bg-textile-warm/90"
              disabled={isLoading}
            >
              {isLoading ? "Creating Admin..." : "Create Admin Account"}
            </Button>
          </form>
          
          <div className="mt-6 space-y-3">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Shield className="h-3 w-3" />
              <span>Secure Firebase Authentication</span>
            </div>
            
            <div className="text-center">
              <Link to="/" className="text-xs text-muted-foreground hover:text-foreground">
                ← Back to Website
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
