import { 
  collection, 
  addDoc, 
  getDocs, 
  deleteDoc, 
  doc, 
  query, 
  orderBy, 
  where, 
  Timestamp,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  DocumentData
} from 'firebase/firestore';
import { db } from './firebase';

// Types for contact messages
export interface ContactMessage {
  id?: string;
  full_name: string;
  email: string;
  company_name?: string;
  phone?: string;
  project_requirements: string;
  created_at: Timestamp;
  updated_at?: Timestamp;
}

export interface ContactMessageInput {
  full_name: string;
  email: string;
  company_name?: string;
  phone?: string;
  project_requirements: string;
}

// Collection reference
const CONTACT_MESSAGES_COLLECTION = 'contact_messages';

// Input validation and sanitization
const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .substring(0, 5000); // Limit length
};

const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

const validateContactMessage = (data: ContactMessageInput): string[] => {
  const errors: string[] = [];
  
  if (!data.full_name || data.full_name.trim().length === 0) {
    errors.push('Full name is required');
  } else if (data.full_name.length > 100) {
    errors.push('Full name must be less than 100 characters');
  }
  
  if (!data.email || !validateEmail(data.email)) {
    errors.push('Valid email is required');
  }
  
  if (!data.project_requirements || data.project_requirements.trim().length === 0) {
    errors.push('Project requirements are required');
  } else if (data.project_requirements.length > 5000) {
    errors.push('Project requirements must be less than 5000 characters');
  }
  
  if (data.company_name && data.company_name.length > 100) {
    errors.push('Company name must be less than 100 characters');
  }
  
  if (data.phone && data.phone.length > 20) {
    errors.push('Phone number must be less than 20 characters');
  }
  
  return errors;
};

// Database operations
export const contactMessageService = {
  // Create a new contact message
  async create(data: ContactMessageInput): Promise<{ success: boolean; id?: string; error?: string }> {
    try {
      // Validate input
      const validationErrors = validateContactMessage(data);
      if (validationErrors.length > 0) {
        return { success: false, error: validationErrors.join(', ') };
      }
      
      // Sanitize input data
      const sanitizedData: Omit<ContactMessage, 'id'> = {
        full_name: sanitizeInput(data.full_name),
        email: sanitizeInput(data.email.toLowerCase()),
        company_name: data.company_name ? sanitizeInput(data.company_name) : undefined,
        phone: data.phone ? sanitizeInput(data.phone) : undefined,
        project_requirements: sanitizeInput(data.project_requirements),
        created_at: Timestamp.now(),
        updated_at: Timestamp.now()
      };
      
      // Remove undefined fields
      Object.keys(sanitizedData).forEach(key => {
        if (sanitizedData[key as keyof typeof sanitizedData] === undefined) {
          delete sanitizedData[key as keyof typeof sanitizedData];
        }
      });
      
      const docRef = await addDoc(collection(db, CONTACT_MESSAGES_COLLECTION), sanitizedData);
      
      return { success: true, id: docRef.id };
    } catch (error) {
      console.error('Error creating contact message:', error);
      return { success: false, error: 'Failed to save message. Please try again.' };
    }
  },
  
  // Get all contact messages (admin only)
  async getAll(pageSize: number = 50, lastDoc?: QueryDocumentSnapshot<DocumentData>): Promise<{
    success: boolean;
    data?: ContactMessage[];
    lastDoc?: QueryDocumentSnapshot<DocumentData>;
    error?: string;
  }> {
    try {
      let q = query(
        collection(db, CONTACT_MESSAGES_COLLECTION),
        orderBy('created_at', 'desc'),
        limit(pageSize)
      );
      
      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }
      
      const querySnapshot = await getDocs(q);
      const messages: ContactMessage[] = [];
      let lastDocument: QueryDocumentSnapshot<DocumentData> | undefined;
      
      querySnapshot.forEach((doc) => {
        messages.push({
          id: doc.id,
          ...doc.data()
        } as ContactMessage);
        lastDocument = doc;
      });
      
      return { success: true, data: messages, lastDoc: lastDocument };
    } catch (error) {
      console.error('Error fetching contact messages:', error);
      return { success: false, error: 'Failed to fetch messages' };
    }
  },
  
  // Delete a contact message (admin only)
  async delete(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!id || id.trim().length === 0) {
        return { success: false, error: 'Invalid message ID' };
      }
      
      await deleteDoc(doc(db, CONTACT_MESSAGES_COLLECTION, id));
      return { success: true };
    } catch (error) {
      console.error('Error deleting contact message:', error);
      return { success: false, error: 'Failed to delete message' };
    }
  },
  
  // Search contact messages (admin only)
  async search(searchTerm: string, pageSize: number = 50): Promise<{
    success: boolean;
    data?: ContactMessage[];
    error?: string;
  }> {
    try {
      if (!searchTerm || searchTerm.trim().length === 0) {
        return this.getAll(pageSize);
      }
      
      const sanitizedSearchTerm = sanitizeInput(searchTerm.toLowerCase());
      
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation. For production, consider using Algolia or Elasticsearch
      const q = query(
        collection(db, CONTACT_MESSAGES_COLLECTION),
        orderBy('created_at', 'desc'),
        limit(pageSize)
      );
      
      const querySnapshot = await getDocs(q);
      const messages: ContactMessage[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data() as ContactMessage;
        const searchableText = [
          data.full_name,
          data.email,
          data.company_name || '',
          data.project_requirements
        ].join(' ').toLowerCase();
        
        if (searchableText.includes(sanitizedSearchTerm)) {
          messages.push({
            id: doc.id,
            ...data
          });
        }
      });
      
      return { success: true, data: messages };
    } catch (error) {
      console.error('Error searching contact messages:', error);
      return { success: false, error: 'Failed to search messages' };
    }
  },
  
  // Get messages by date range (admin only)
  async getByDateRange(startDate: Date, endDate: Date): Promise<{
    success: boolean;
    data?: ContactMessage[];
    error?: string;
  }> {
    try {
      const q = query(
        collection(db, CONTACT_MESSAGES_COLLECTION),
        where('created_at', '>=', Timestamp.fromDate(startDate)),
        where('created_at', '<=', Timestamp.fromDate(endDate)),
        orderBy('created_at', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const messages: ContactMessage[] = [];
      
      querySnapshot.forEach((doc) => {
        messages.push({
          id: doc.id,
          ...doc.data()
        } as ContactMessage);
      });
      
      return { success: true, data: messages };
    } catch (error) {
      console.error('Error fetching messages by date range:', error);
      return { success: false, error: 'Failed to fetch messages' };
    }
  }
};

// Export utility functions
export { sanitizeInput, validateEmail, validateContactMessage };
