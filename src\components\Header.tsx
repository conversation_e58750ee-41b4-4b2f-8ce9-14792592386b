import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, Globe, Mail, Phone, X } from "lucide-react";

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false); // Close mobile menu after navigation
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="bg-background/95 backdrop-blur-sm border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Globe className="h-8 w-8 text-textile-warm" />
            <div>
              <h1 className="text-2xl font-bold text-primary">CottonXpert</h1>
              <p className="text-xs text-muted-foreground">Textile Sourcing & Inspection</p>
            </div>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => scrollToSection('home')}
              className="text-foreground hover:text-textile-warm transition-colors"
            >
              Home
            </button>
            <button
              onClick={() => scrollToSection('about')}
              className="text-foreground hover:text-textile-warm transition-colors"
            >
              About
            </button>
            <button
              onClick={() => scrollToSection('products')}
              className="text-foreground hover:text-textile-warm transition-colors"
            >
              Products
            </button>
            <button
              onClick={() => scrollToSection('partnership')}
              className="text-foreground hover:text-textile-warm transition-colors"
            >
              Partnership
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="text-foreground hover:text-textile-warm transition-colors"
            >
              Contact
            </button>
          </nav>

          <div className="hidden lg:flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Mail className="h-4 w-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Phone className="h-4 w-4" />
              <span>+92-321-8765432</span>
            </div>
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={toggleMobileMenu}
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 pb-4 border-t border-border">
            <nav className="flex flex-col space-y-4 pt-4">
              <button
                onClick={() => scrollToSection('home')}
                className="text-left text-foreground hover:text-textile-warm transition-colors py-2"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="text-left text-foreground hover:text-textile-warm transition-colors py-2"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection('products')}
                className="text-left text-foreground hover:text-textile-warm transition-colors py-2"
              >
                Products
              </button>
              <button
                onClick={() => scrollToSection('partnership')}
                className="text-left text-foreground hover:text-textile-warm transition-colors py-2"
              >
                Partnership
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="text-left text-foreground hover:text-textile-warm transition-colors py-2"
              >
                Contact
              </button>

              {/* Mobile Contact Info */}
              <div className="pt-4 border-t border-border space-y-3">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>+92-321-8765432</span>
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}