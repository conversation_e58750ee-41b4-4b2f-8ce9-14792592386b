import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Award, Globe, Users, TrendingUp, MapPin, Building } from "lucide-react";

const stats = [
  { label: "Years of Experience", value: "20+", icon: Award },
  { label: "Global Partners", value: "50+", icon: Globe },
  { label: "Satisfied Clients", value: "200+", icon: Users },
  { label: "Quality Rate", value: "99.8%", icon: TrendingUp }
];

const locations = [
  {
    country: "Pakistan",
    city: "Karachi",
    company: "CottonXpert",
    role: "Headquarters & Operations",
    icon: Building
  },
  {
    country: "United Kingdom", 
    city: "London",
    company: "Omnia Ventures Limited",
    role: "Strategic Partner",
    icon: MapPin
  }
];

export function About() {
  return (
    <section id="about" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-primary/10 text-primary border-primary/20">
            About CottonXpert
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-primary">
            Two Decades of Textile Excellence
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
            Since our founding, CottonXpert has been at the forefront of textile sourcing 
            and inspection, building lasting partnerships and delivering uncompromising quality 
            to institutional and retail clients worldwide.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={stat.label} className="text-center hover:shadow-card-hover transition-all duration-300">
              <CardContent className="p-6">
                <stat.icon className="h-8 w-8 text-textile-warm mx-auto mb-4" />
                <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h3 className="text-3xl font-bold mb-6 text-primary">Our Mission</h3>
            <p className="text-lg text-muted-foreground mb-6">
              To bridge the gap between textile manufacturers and global markets by providing 
              comprehensive sourcing and inspection services that ensure quality, reliability, 
              and customer satisfaction.
            </p>
            <p className="text-lg text-muted-foreground">
              We specialize in institutional and retail linen, working closely with hotels, 
              hospitals, restaurants, and retail chains to deliver products that meet the 
              highest standards of quality and durability.
            </p>
          </div>

          <div>
            <h3 className="text-3xl font-bold mb-6 text-primary">Our Expertise</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Award className="h-6 w-6 text-textile-warm mt-1" />
                <div>
                  <h4 className="font-semibold text-primary">Quality Inspection</h4>
                  <p className="text-muted-foreground">Rigorous testing and inspection protocols</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Globe className="h-6 w-6 text-textile-warm mt-1" />
                <div>
                  <h4 className="font-semibold text-primary">Global Sourcing</h4>
                  <p className="text-muted-foreground">Worldwide network of trusted suppliers</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Users className="h-6 w-6 text-textile-warm mt-1" />
                <div>
                  <h4 className="font-semibold text-primary">Custom Solutions</h4>
                  <p className="text-muted-foreground">Tailored services for unique requirements</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div id="partnership">
          <h3 className="text-3xl font-bold text-center mb-12 text-primary">Global Partnership</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {locations.map((location, index) => (
              <Card key={location.company} className="hover:shadow-card-hover transition-all duration-300">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <location.icon className="h-8 w-8 text-textile-warm mr-3" />
                    <div>
                      <h4 className="text-xl font-bold text-primary">{location.company}</h4>
                      <p className="text-muted-foreground">{location.role}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-lg font-semibold text-foreground">{location.city}, {location.country}</p>
                    <p className="text-muted-foreground">
                      {index === 0 
                        ? "Our main headquarters where we manage operations, quality control, and client relationships across Asia-Pacific region."
                        : "Strategic partnership hub for European and North American markets, providing local support and business development."
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}